import type { TLoading } from "@types";
import CategorySkeleton from "../skeletons/CategorySkeleton/CategorySkeleton"
import ProductSkeleton from "../skeletons/ProductSkeleton/ProductSkeleton";
import CartSkeleton from "../skeletons/CartSkeleton/CartSkeleton";
import <PERSON><PERSON>Handler from "../LottieHandler/LottieHandler";

const skeletonTypes = {
  "category" : CategorySkeleton,
  "product" : ProductSkeleton,
  "cart" : CartSkeleton,
}

type LoadingProps ={
  loading: TLoading;
  error: null | string;
  children: React.ReactNode;
  type?: keyof typeof skeletonTypes;
}


const Loading = ({loading, error, children, type ="category"}: LoadingProps) => {
  const Component = skeletonTypes[type];
  return (
    <>
        {loading === "pending" && <Component />}
        {loading === "succeeded" && children}
        {loading === "failed" && <LottieHandler type="error" message={error as string} />}
    </>
  )
}

export default Loading