{"name": "dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "lottie-react": "^2.4.1", "react": "^19.1.1", "react-bootstrap": "^2.10.10", "react-content-loader": "^7.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.8.0", "redux-persist": "^6.0.0", "tailwindcss": "^4.1.11", "vite-plugin-svgr": "^4.3.0"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.1.0"}}