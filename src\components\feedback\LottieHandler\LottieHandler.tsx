type LottieHandlerProps = {
  type: "notFound" | "empty" | "loading" | "error";
  message?: string;
};

const LottieHandler = ({ type, message }: LottieHandlerProps) => {
  // Simple fallback components for different states
  const renderFallback = () => {
    switch (type) {
      case "loading":
        return (
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-indigo-600"></div>
          </div>
        );
      case "error":
        return (
          <div className="flex items-center justify-center">
            <div className="text-red-500 text-6xl">⚠️</div>
          </div>
        );
      case "empty":
        return (
          <div className="flex items-center justify-center">
            <div className="text-gray-400 text-6xl">📭</div>
          </div>
        );
      case "notFound":
        return (
          <div className="flex items-center justify-center">
            <div className="text-gray-400 text-6xl">🔍</div>
          </div>
        );
      default:
        return null;
    }
  };

  const messageStyle =
    type === "error"
      ? "text-lg text-red-600 mt-4"
      : "text-lg text-gray-600 mt-4";

  return (
    <div className="flex flex-col items-center justify-center p-8">
      {renderFallback()}
      {message && <h3 className={messageStyle}>{message}</h3>}
    </div>
  );
};

export default LottieHandler;
